{"name": "MapboxService", "displayName": "Mapbox 地图服务", "version": "1.0.0", "description": "基于Mapbox API的地图服务插件，支持地理编码、路线规划、距离矩阵和静态地图生成", "author": "VCP Team", "pluginType": "synchronous", "entryPoint": "node MapboxService.js", "communication": {"protocol": "stdio"}, "configSchema": {"MAPBOX_ACCESS_TOKEN": {"type": "string", "description": "Mapbox Access Token，从 https://account.mapbox.com/ 获取", "required": true, "sensitive": true}, "HTTP_PROXY": {"type": "string", "description": "HTTP代理服务器地址（可选）", "required": false}, "HTTPS_PROXY": {"type": "string", "description": "HTTPS代理服务器地址（可选）", "required": false}}, "capabilities": {"invocationCommands": [{"command": "geocoding", "description": "地理编码 - 将地址转换为坐标\n\n参数说明：\n- query (必需): 要搜索的地址或地点名称\n- limit (可选): 返回结果数量，默认5，最大10\n- proximity (可选): 优先搜索的坐标点，格式\"longitude,latitude\"\n- bbox (可选): 搜索边界框，格式\"minX,minY,maxX,maxY\"\n- country (可选): 限制搜索的国家代码，如\"CN\"\n- types (可选): 限制搜索的地点类型，如\"poi,address\"\n- language (可选): 返回语言，默认\"zh\"\n\n调用格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MapboxService「末」,\ncommand:「始」geocoding「末」,\nquery:「始」北京天安门广场「末」,\nlimit:「始」5「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回格式：包含地点名称、坐标、边界框和详细属性信息", "example": "搜索地址：query=\"上海外滩\", limit=3"}, {"command": "reverse_geocoding", "description": "反向地理编码 - 将坐标转换为地址\n\n参数说明：\n- longitude (必需): 经度\n- latitude (必需): 纬度\n- types (可选): 返回的地点类型，如\"poi,address\"\n- language (可选): 返回语言，默认\"zh\"\n\n调用格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MapboxService「末」,\ncommand:「始」reverse_geocoding「末」,\nlongitude:「始」116.3974「末」,\nlatitude:「始」39.9093「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回格式：包含该坐标点的地址信息和周边地点", "example": "坐标转地址：longitude=121.4737, latitude=31.2304"}, {"command": "directions", "description": "路线规划 - 计算两点或多点间的路线\n\n参数说明：\n- coordinates (必需): 坐标数组，格式[[lng1,lat1],[lng2,lat2],...]\n- profile (可选): 出行方式，可选\"driving\"(驾车)、\"walking\"(步行)、\"cycling\"(骑行)，默认\"driving\"\n- alternatives (可选): 是否返回备选路线，默认false\n- steps (可选): 是否返回详细步骤，默认true\n- overview (可选): 路线详细程度，可选\"full\"、\"simplified\"、\"false\"，默认\"full\"\n- language (可选): 指令语言，默认\"zh\"\n\n调用格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MapboxService「末」,\ncommand:「始」directions「末」,\ncoordinates:「始」[[116.3974,39.9093],[116.4074,39.9193]]「末」,\nprofile:「始」driving「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回格式：包含距离、时间、路线几何和详细导航步骤", "example": "路线规划：从天安门到故宫，驾车路线"}, {"command": "matrix", "description": "距离矩阵 - 计算多个点之间的距离和时间\n\n参数说明：\n- coordinates (必需): 坐标数组，格式[[lng1,lat1],[lng2,lat2],...]\n- profile (可选): 出行方式，默认\"driving\"\n- sources (可选): 起点索引数组，如\"0,1\"\n- destinations (可选): 终点索引数组，如\"2,3\"\n- annotations (可选): 返回数据类型，默认\"duration,distance\"\n\n调用格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MapboxService「末」,\ncommand:「始」matrix「末」,\ncoordinates:「始」[[116.3974,39.9093],[116.4074,39.9193],[116.4174,39.9293]]「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回格式：包含所有点对之间的距离矩阵和时间矩阵", "example": "距离矩阵：计算3个地点之间的距离和时间"}, {"command": "static_map", "description": "静态地图生成 - 生成静态地图图片URL\n\n参数说明：\n- longitude (可选): 中心点经度，默认116.3974(北京)\n- latitude (可选): 中心点纬度，默认39.9093(北京)\n- zoom (可选): 缩放级别，范围0-22，默认12\n- width (可选): 图片宽度，默认600，最大1280\n- height (可选): 图片高度，默认400，最大1280\n- style (可选): 地图样式，默认\"streets-v11\"\n- markers (可选): 标记数组，格式[{longitude,latitude,color,size,label}]\n\n调用格式：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MapboxService「末」,\ncommand:「始」static_map「末」,\nlongitude:「始」116.3974「末」,\nlatitude:「始」39.9093「末」,\nzoom:「始」15「末」,\nmarkers:「始」[{\"longitude\":116.3974,\"latitude\":39.9093,\"color\":\"red\"}]「末」\n<<<[END_TOOL_REQUEST]>>>\n\n返回格式：包含地图图片URL和地图参数信息", "example": "生成静态地图：北京天安门，带红色标记"}]}, "dependencies": {"axios": "^1.6.0", "https-proxy-agent": "^7.0.0", "http-proxy-agent": "^7.0.0"}, "tags": ["map", "geocoding", "directions", "location", "api"], "category": "Location Services", "license": "CC BY-NC-SA 4.0"}