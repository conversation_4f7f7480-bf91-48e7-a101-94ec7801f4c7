{"name": "vcp-mapbox-service", "version": "1.0.0", "description": "Mapbox map service plugin for VCP", "main": "MapboxService.js", "scripts": {"test": "node test.js", "install": "npm install"}, "keywords": ["mapbox", "map", "geocoding", "directions", "location", "vcp", "plugin"], "author": "VCP Team", "license": "CC BY-NC-SA 4.0", "dependencies": {"axios": "^1.6.0", "https-proxy-agent": "^7.0.0", "http-proxy-agent": "^7.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/lioensky/VCPToolBox"}}