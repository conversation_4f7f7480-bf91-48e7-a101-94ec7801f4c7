const { spawn } = require('child_process');

// 测试数据
const testCases = [
    {
        name: "地理编码测试 - 北京天安门",
        input: {
            command: "geocoding",
            query: "北京天安门广场",
            limit: 3
        }
    },
    {
        name: "反向地理编码测试 - 北京坐标",
        input: {
            command: "reverse_geocoding",
            longitude: 116.3974,
            latitude: 39.9093
        }
    },
    {
        name: "路线规划测试 - 天安门到故宫",
        input: {
            command: "directions",
            coordinates: [[116.3974, 39.9093], [116.3972, 39.9163]],
            profile: "walking"
        }
    },
    {
        name: "距离矩阵测试 - 北京三个地点",
        input: {
            command: "matrix",
            coordinates: [
                [116.3974, 39.9093], // 天安门
                [116.3972, 39.9163], // 故宫
                [116.4074, 39.9042]  // 王府井
            ],
            profile: "driving"
        }
    },
    {
        name: "静态地图测试 - 北京地图",
        input: {
            command: "static_map",
            longitude: 116.3974,
            latitude: 39.9093,
            zoom: 15,
            width: 800,
            height: 600,
            markers: [
                {
                    longitude: 116.3974,
                    latitude: 39.9093,
                    color: "red",
                    size: "large",
                    label: "A"
                }
            ]
        }
    }
];

async function runTest(testCase) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 运行测试: ${testCase.name}`);
        console.log(`📝 输入参数:`, JSON.stringify(testCase.input, null, 2));
        
        const child = spawn('node', ['MapboxService.js'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        let output = '';
        let error = '';
        
        child.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        child.stderr.on('data', (data) => {
            error += data.toString();
        });
        
        child.on('close', (code) => {
            try {
                if (error) {
                    console.log(`⚠️ 调试信息:`, error);
                }
                
                if (output) {
                    const result = JSON.parse(output.trim());
                    console.log(`✅ 测试结果:`, JSON.stringify(result, null, 2));
                    
                    if (result.status === 'success') {
                        console.log(`🎉 ${testCase.name} - 成功`);
                        
                        // 显示具体结果摘要
                        const res = result.result;
                        switch (testCase.input.command) {
                            case 'geocoding':
                                console.log(`   📍 找到 ${res.result_count} 个地点`);
                                if (res.results.length > 0) {
                                    console.log(`   🏷️ 第一个结果: ${res.results[0].place_name_zh}`);
                                    console.log(`   📐 坐标: [${res.results[0].center[0]}, ${res.results[0].center[1]}]`);
                                }
                                break;
                                
                            case 'reverse_geocoding':
                                console.log(`   📍 找到 ${res.result_count} 个地址`);
                                if (res.results.length > 0) {
                                    console.log(`   🏷️ 地址: ${res.results[0].place_name_zh}`);
                                }
                                break;
                                
                            case 'directions':
                                if (res.routes.length > 0) {
                                    const route = res.routes[0];
                                    console.log(`   📏 距离: ${(route.distance / 1000).toFixed(2)} 公里`);
                                    console.log(`   ⏱️ 时间: ${Math.round(route.duration / 60)} 分钟`);
                                    console.log(`   🛣️ 路段数: ${route.legs.length}`);
                                }
                                break;
                                
                            case 'matrix':
                                console.log(`   📊 矩阵大小: ${res.durations.length}x${res.durations[0]?.length || 0}`);
                                if (res.durations.length > 1) {
                                    console.log(`   ⏱️ 第一到第二点时间: ${Math.round(res.durations[0][1] / 60)} 分钟`);
                                    console.log(`   📏 第一到第二点距离: ${(res.distances[0][1] / 1000).toFixed(2)} 公里`);
                                }
                                break;
                                
                            case 'static_map':
                                console.log(`   🗺️ 地图URL: ${res.map_url.substring(0, 80)}...`);
                                console.log(`   📐 中心点: [${res.center[0]}, ${res.center[1]}]`);
                                console.log(`   🔍 缩放级别: ${res.zoom}`);
                                console.log(`   📏 尺寸: ${res.size[0]}x${res.size[1]}`);
                                break;
                        }
                    } else {
                        console.log(`⚠️ ${testCase.name} - 失败: ${result.error}`);
                    }
                } else {
                    console.log(`❌ ${testCase.name} - 无输出`);
                }
                
                resolve();
            } catch (parseError) {
                console.log(`❌ ${testCase.name} - JSON解析错误:`, parseError.message);
                console.log(`原始输出:`, output);
                resolve();
            }
        });
        
        child.on('error', (err) => {
            console.log(`❌ ${testCase.name} - 进程错误:`, err.message);
            resolve();
        });
        
        // 发送测试数据
        child.stdin.write(JSON.stringify(testCase.input));
        child.stdin.end();
    });
}

async function runAllTests() {
    console.log('🚀 开始Mapbox地图服务插件测试');
    console.log('📋 注意: 需要在config.env中配置有效的MAPBOX_ACCESS_TOKEN');
    
    for (let i = 0; i < testCases.length; i++) {
        await runTest(testCases[i]);
        
        // 添加延迟以避免速率限制
        if (i < testCases.length - 1) {
            console.log('   ⏳ 等待1秒后继续下一个测试...');
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    console.log('\n✨ 所有测试完成');
    console.log('\n📖 使用说明:');
    console.log('1. 确保已配置Mapbox Access Token');
    console.log('2. 免费版本限制: 50,000请求/月');
    console.log('3. 支持全球地理编码和路线规划');
    console.log('4. 坐标格式: [经度, 纬度]');
}

// 检查配置文件
const fs = require('fs');
const path = require('path');
const configPath = path.join(__dirname, 'config.env');

if (!fs.existsSync(configPath)) {
    console.log('⚠️ 警告: 未找到config.env文件');
    console.log('📝 请复制config.env.example为config.env并配置Mapbox Access Token');
    console.log('🔗 获取Token: https://account.mapbox.com/');
} else {
    const config = fs.readFileSync(configPath, 'utf8');
    if (!config.includes('MAPBOX_ACCESS_TOKEN=') || config.includes('your_access_token_here')) {
        console.log('⚠️ 警告: Mapbox Access Token未配置');
        console.log('📝 请在config.env中设置有效的MAPBOX_ACCESS_TOKEN');
    }
}

if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runTest, runAllTests };
