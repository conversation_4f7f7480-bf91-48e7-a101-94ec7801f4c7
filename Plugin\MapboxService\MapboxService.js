const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { HttpsProxyAgent } = require('https-proxy-agent');
const { HttpProxyAgent } = require('http-proxy-agent');

class MapboxService {
    constructor() {
        this.accessToken = null;
        this.baseURL = 'https://api.mapbox.com';
        this.proxyConfig = null;
        this.loadConfig();
    }

    loadConfig() {
        try {
            // 尝试从插件目录加载配置
            const configPath = path.join(__dirname, 'config.env');
            
            if (fs.existsSync(configPath)) {
                const config = fs.readFileSync(configPath, 'utf8');
                const lines = config.split('\n');
                for (const line of lines) {
                    if (line.startsWith('MAPBOX_ACCESS_TOKEN=')) {
                        this.accessToken = line.split('=')[1].trim().replace(/['"]/g, '');
                    } else if (line.startsWith('HTTP_PROXY=')) {
                        this.proxyConfig = line.split('=')[1].trim().replace(/['"]/g, '');
                    } else if (line.startsWith('HTTPS_PROXY=')) {
                        this.proxyConfig = line.split('=')[1].trim().replace(/['"]/g, '');
                    }
                }
            }
            
            // 如果插件配置中没有，尝试从环境变量获取
            if (!this.accessToken) {
                this.accessToken = process.env.MAPBOX_ACCESS_TOKEN;
            }
            
            // 从环境变量获取代理配置
            if (!this.proxyConfig) {
                this.proxyConfig = process.env.https_proxy || process.env.HTTPS_PROXY || 
                                 process.env.http_proxy || process.env.HTTP_PROXY;
            }
        } catch (error) {
            console.error('Error loading Mapbox config:', error);
        }
    }

    async makeRequest(endpoint, params = {}) {
        if (!this.accessToken) {
            throw new Error('Mapbox Access Token not configured. Please set MAPBOX_ACCESS_TOKEN in config.');
        }

        try {
            const requestConfig = {
                params: {
                    ...params,
                    access_token: this.accessToken
                },
                timeout: 30000,
                maxRedirects: 5
            };

            // 如果配置了代理，添加代理设置
            if (this.proxyConfig) {
                if (endpoint.startsWith('https://')) {
                    requestConfig.httpsAgent = new HttpsProxyAgent(this.proxyConfig);
                } else {
                    requestConfig.httpAgent = new HttpProxyAgent(this.proxyConfig);
                }
            }

            const response = await axios.get(`${this.baseURL}${endpoint}`, requestConfig);
            return response.data;
        } catch (error) {
            if (error.response) {
                throw new Error(`Mapbox API Error: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
            } else if (error.code === 'ETIMEDOUT' || error.code === 'ECONNRESET') {
                throw new Error(`Network timeout: Unable to connect to Mapbox API. Please check your network connection.`);
            } else {
                throw new Error(`Network Error: ${error.message}`);
            }
        }
    }

    async geocoding(query, options = {}) {
        const params = {
            limit: options.limit || 5,
            proximity: options.proximity || undefined,
            bbox: options.bbox || undefined,
            country: options.country || undefined,
            types: options.types || undefined,
            language: options.language || 'zh'
        };

        // 移除undefined值
        Object.keys(params).forEach(key => params[key] === undefined && delete params[key]);

        const data = await this.makeRequest(`/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json`, params);
        
        const features = data.features || [];
        const results = features.map(feature => ({
            place_name: feature.place_name,
            place_name_zh: feature.place_name_zh || feature.place_name,
            center: feature.center, // [longitude, latitude]
            bbox: feature.bbox,
            properties: {
                address: feature.properties?.address || '',
                category: feature.properties?.category || '',
                landmark: feature.properties?.landmark || false
            },
            context: feature.context || []
        }));

        return {
            query: query,
            results: results,
            result_count: results.length
        };
    }

    async reverseGeocoding(longitude, latitude, options = {}) {
        const params = {
            types: options.types || undefined,
            language: options.language || 'zh'
        };

        // 移除undefined值
        Object.keys(params).forEach(key => params[key] === undefined && delete params[key]);

        const data = await this.makeRequest(`/geocoding/v5/mapbox.places/${longitude},${latitude}.json`, params);
        
        const features = data.features || [];
        const results = features.map(feature => ({
            place_name: feature.place_name,
            place_name_zh: feature.place_name_zh || feature.place_name,
            center: feature.center,
            properties: {
                address: feature.properties?.address || '',
                category: feature.properties?.category || ''
            },
            context: feature.context || []
        }));

        return {
            coordinates: [longitude, latitude],
            results: results,
            result_count: results.length
        };
    }

    async directions(coordinates, options = {}) {
        if (!coordinates || coordinates.length < 2) {
            throw new Error('At least 2 coordinates are required for directions');
        }

        const profile = options.profile || 'driving'; // driving, walking, cycling
        const coordinatesStr = coordinates.map(coord => `${coord[0]},${coord[1]}`).join(';');
        
        const params = {
            alternatives: options.alternatives || false,
            geometries: 'geojson',
            steps: options.steps || true,
            overview: options.overview || 'full',
            language: options.language || 'zh'
        };

        const data = await this.makeRequest(`/directions/v5/mapbox/${profile}/${coordinatesStr}`, params);
        
        const routes = data.routes || [];
        const processedRoutes = routes.map(route => ({
            distance: route.distance, // 米
            duration: route.duration, // 秒
            geometry: route.geometry,
            legs: route.legs?.map(leg => ({
                distance: leg.distance,
                duration: leg.duration,
                steps: leg.steps?.map(step => ({
                    distance: step.distance,
                    duration: step.duration,
                    instruction: step.maneuver?.instruction || '',
                    name: step.name || ''
                })) || []
            })) || []
        }));

        return {
            coordinates: coordinates,
            profile: profile,
            routes: processedRoutes,
            route_count: processedRoutes.length
        };
    }

    async matrix(coordinates, options = {}) {
        if (!coordinates || coordinates.length < 2) {
            throw new Error('At least 2 coordinates are required for matrix');
        }

        const profile = options.profile || 'driving';
        const coordinatesStr = coordinates.map(coord => `${coord[0]},${coord[1]}`).join(';');
        
        const params = {
            sources: options.sources || undefined,
            destinations: options.destinations || undefined,
            annotations: options.annotations || 'duration,distance'
        };

        // 移除undefined值
        Object.keys(params).forEach(key => params[key] === undefined && delete params[key]);

        const data = await this.makeRequest(`/directions-matrix/v1/mapbox/${profile}/${coordinatesStr}`, params);
        
        return {
            coordinates: coordinates,
            profile: profile,
            durations: data.durations || [], // 秒
            distances: data.distances || [], // 米
            sources: data.sources || [],
            destinations: data.destinations || []
        };
    }

    async staticMap(options = {}) {
        const {
            longitude = 116.3974, // 默认北京
            latitude = 39.9093,
            zoom = 12,
            width = 600,
            height = 400,
            style = 'streets-v11',
            markers = [],
            overlay = ''
        } = options;

        let url = `${this.baseURL}/styles/v1/mapbox/${style}/static`;
        
        // 添加标记
        if (markers && markers.length > 0) {
            const markerStr = markers.map(marker => {
                const { longitude: lng, latitude: lat, color = 'red', size = 'medium', label = '' } = marker;
                return `pin-${size}-${label}+${color}(${lng},${lat})`;
            }).join(',');
            url += `/${markerStr}`;
        }

        // 添加覆盖层
        if (overlay) {
            url += `/${overlay}`;
        }

        url += `/${longitude},${latitude},${zoom}/${width}x${height}`;
        
        const params = {
            access_token: this.accessToken
        };

        return {
            map_url: url + '?' + new URLSearchParams(params).toString(),
            center: [longitude, latitude],
            zoom: zoom,
            size: [width, height],
            style: style
        };
    }
}

// 主执行函数
async function main() {
    try {
        const input = process.stdin;
        let data = '';
        
        input.on('data', chunk => {
            data += chunk;
        });
        
        input.on('end', async () => {
            try {
                const params = JSON.parse(data.trim());
                const mapbox = new MapboxService();
                
                let result;
                
                switch (params.command) {
                    case 'geocoding':
                        if (!params.query) {
                            throw new Error('Query parameter is required for geocoding');
                        }
                        result = await mapbox.geocoding(params.query, params);
                        break;
                        
                    case 'reverse_geocoding':
                        if (params.longitude === undefined || params.latitude === undefined) {
                            throw new Error('Longitude and latitude parameters are required for reverse geocoding');
                        }
                        result = await mapbox.reverseGeocoding(params.longitude, params.latitude, params);
                        break;
                        
                    case 'directions':
                        if (!params.coordinates || !Array.isArray(params.coordinates)) {
                            throw new Error('Coordinates array is required for directions');
                        }
                        result = await mapbox.directions(params.coordinates, params);
                        break;
                        
                    case 'matrix':
                        if (!params.coordinates || !Array.isArray(params.coordinates)) {
                            throw new Error('Coordinates array is required for matrix');
                        }
                        result = await mapbox.matrix(params.coordinates, params);
                        break;
                        
                    case 'static_map':
                        result = await mapbox.staticMap(params);
                        break;
                        
                    default:
                        throw new Error(`Unknown command: ${params.command}`);
                }
                
                console.log(JSON.stringify({
                    status: 'success',
                    result: result,
                    messageForAI: `Mapbox ${params.command} completed successfully.`
                }));
                
            } catch (error) {
                console.log(JSON.stringify({
                    status: 'error',
                    error: error.message,
                    messageForAI: `Mapbox API error: ${error.message}`
                }));
            }
        });
        
    } catch (error) {
        console.log(JSON.stringify({
            status: 'error',
            error: error.message,
            messageForAI: `Mapbox plugin initialization error: ${error.message}`
        }));
    }
}

if (require.main === module) {
    main();
}

module.exports = MapboxService;
