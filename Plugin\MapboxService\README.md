# MapboxService Plugin

基于Mapbox API的地图服务插件，为VCP系统提供强大的地理位置和地图功能。

## 功能特性

### 🗺️ 地理编码 (Geocoding)
- 地址转坐标：将地址、地点名称转换为经纬度坐标
- 支持中文地址搜索
- 可设置搜索范围和类型过滤
- 返回详细的地点信息和边界框

### 🔄 反向地理编码 (Reverse Geocoding)
- 坐标转地址：将经纬度坐标转换为可读地址
- 获取坐标点的详细位置信息
- 支持多种地点类型返回

### 🛣️ 路线规划 (Directions)
- 多点路线规划：支持2个或多个点的路线计算
- 多种出行方式：驾车、步行、骑行
- 详细导航指令：包含转向、距离、时间信息
- 路线几何数据：可用于地图绘制

### 📊 距离矩阵 (Matrix)
- 批量距离计算：计算多个点之间的距离和时间
- 支持自定义起点和终点
- 返回完整的距离和时间矩阵

### 🖼️ 静态地图 (Static Map)
- 生成静态地图图片URL
- 支持自定义标记和样式
- 可调整地图大小和缩放级别
- 不计入API调用次数

## API限制

### 免费版本
- **月度限制**: 50,000次请求/月
- **速率限制**: 600请求/分钟
- **地图样式**: 包含多种免费样式

### 付费版本优势
- 更高的请求限制
- 更多地图样式选择
- 高级路线优化
- 实时交通信息

## 配置说明

1. 访问 [Mapbox Account](https://account.mapbox.com/)
2. 注册或登录账户（免费）
3. 在Dashboard中找到 "Access tokens"
4. 复制默认的Public token或创建新token
5. 将Access Token填入配置文件

## 使用示例

### 地理编码
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」MapboxService「末」,
command:「始」geocoding「末」,
query:「始」北京天安门广场「末」,
limit:「始」5「末」
<<<[END_TOOL_REQUEST]>>>
```

### 反向地理编码
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」MapboxService「末」,
command:「始」reverse_geocoding「末」,
longitude:「始」116.3974「末」,
latitude:「始」39.9093「末」
<<<[END_TOOL_REQUEST]>>>
```

### 路线规划
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」MapboxService「末」,
command:「始」directions「末」,
coordinates:「始」[[116.3974,39.9093],[116.4074,39.9193]]「末」,
profile:「始」driving「末」
<<<[END_TOOL_REQUEST]>>>
```

### 距离矩阵
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」MapboxService「末」,
command:「始」matrix「末」,
coordinates:「始」[[116.3974,39.9093],[116.4074,39.9193],[116.4174,39.9293]]「末」
<<<[END_TOOL_REQUEST]>>>
```

### 静态地图
```
<<<[TOOL_REQUEST]>>>
tool_name:「始」MapboxService「末」,
command:「始」static_map「末」,
longitude:「始」116.3974「末」,
latitude:「始」39.9093「末」,
zoom:「始」15「末」,
markers:「始」[{"longitude":116.3974,"latitude":39.9093,"color":"red"}]「末」
<<<[END_TOOL_REQUEST]>>>
```

## 返回数据格式

### 地理编码结果
```json
{
  "query": "北京天安门广场",
  "results": [
    {
      "place_name": "天安门广场, 北京市, 中国",
      "place_name_zh": "天安门广场",
      "center": [116.3974, 39.9093],
      "bbox": [116.3874, 39.8993, 116.4074, 39.9193],
      "properties": {
        "address": "天安门广场",
        "category": "landmark"
      }
    }
  ],
  "result_count": 1
}
```

### 路线规划结果
```json
{
  "coordinates": [[116.3974, 39.9093], [116.4074, 39.9193]],
  "profile": "driving",
  "routes": [
    {
      "distance": 1500,
      "duration": 300,
      "geometry": {...},
      "legs": [
        {
          "distance": 1500,
          "duration": 300,
          "steps": [
            {
              "distance": 500,
              "duration": 100,
              "instruction": "向北行驶",
              "name": "长安街"
            }
          ]
        }
      ]
    }
  ]
}
```

## 应用场景

### 🏢 位置服务
- 地址标准化和验证
- 坐标转换和地址解析
- 位置搜索和推荐

### 🚗 出行规划
- 路线导航和规划
- 多点路径优化
- 出行时间估算

### 📊 地理分析
- 距离计算和分析
- 服务范围分析
- 地理数据可视化

### 🗺️ 地图应用
- 静态地图生成
- 位置标记和展示
- 地图样式定制

## 坐标系统

Mapbox使用WGS84坐标系统：
- **经度 (Longitude)**: 东西方向，范围 -180 到 180
- **纬度 (Latitude)**: 南北方向，范围 -90 到 90
- **格式**: [经度, 纬度] 或 [longitude, latitude]

### 中国主要城市坐标参考
- **北京**: [116.3974, 39.9093]
- **上海**: [121.4737, 31.2304]
- **广州**: [113.2644, 23.1291]
- **深圳**: [114.0579, 22.5431]
- **杭州**: [120.1551, 30.2741]

## 错误处理

插件会自动处理常见错误：
- Access Token无效
- 网络连接问题
- 参数格式错误
- API限制超出

错误信息会以标准格式返回，便于AI理解和处理。

## 依赖项

- Node.js >= 14.0.0
- axios >= 1.6.0
- https-proxy-agent >= 7.0.0
- http-proxy-agent >= 7.0.0

## 注意事项

1. **Access Token安全**: 请妥善保管您的Access Token，不要在代码中硬编码
2. **请求限制**: 合理控制请求频率，避免超出限制
3. **坐标格式**: 注意坐标顺序为[经度, 纬度]
4. **网络环境**: 如需代理访问，请在配置文件中设置

## 许可证

本插件遵循 CC BY-NC-SA 4.0 许可证。
