const MapboxService = require('./MapboxService.js');

// 模拟测试（不需要真实API Token）
async function mockTest() {
    console.log('🧪 MapboxService插件模拟测试');
    console.log('================================');
    
    // 测试1: 配置加载
    console.log('\n📋 测试1: 配置加载');
    const mapbox = new MapboxService();
    
    if (mapbox.accessToken) {
        console.log('✅ Access Token加载成功');
        console.log(`   Token长度: ${mapbox.accessToken.length}`);
        console.log(`   Token前缀: ${mapbox.accessToken.substring(0, 20)}...`);
    } else {
        console.log('❌ Access Token未加载');
    }
    
    // 测试2: 参数验证
    console.log('\n📋 测试2: 参数验证');
    
    const testCases = [
        {
            name: '地理编码 - 有效参数',
            input: {
                command: 'geocoding',
                query: '北京天安门',
                limit: 5
            }
        },
        {
            name: '地理编码 - 缺少query',
            input: {
                command: 'geocoding',
                limit: 5
            }
        },
        {
            name: '反向地理编码 - 有效参数',
            input: {
                command: 'reverse_geocoding',
                longitude: 116.3974,
                latitude: 39.9093
            }
        },
        {
            name: '反向地理编码 - 缺少坐标',
            input: {
                command: 'reverse_geocoding',
                longitude: 116.3974
            }
        },
        {
            name: '路线规划 - 有效参数',
            input: {
                command: 'directions',
                coordinates: [[116.3974, 39.9093], [116.4074, 39.9193]],
                profile: 'driving'
            }
        },
        {
            name: '路线规划 - 坐标不足',
            input: {
                command: 'directions',
                coordinates: [[116.3974, 39.9093]],
                profile: 'driving'
            }
        },
        {
            name: '距离矩阵 - 有效参数',
            input: {
                command: 'matrix',
                coordinates: [[116.3974, 39.9093], [116.4074, 39.9193], [116.4174, 39.9293]]
            }
        },
        {
            name: '静态地图 - 默认参数',
            input: {
                command: 'static_map'
            }
        },
        {
            name: '未知命令',
            input: {
                command: 'unknown_command'
            }
        }
    ];
    
    for (const testCase of testCases) {
        console.log(`\n   🔍 ${testCase.name}`);
        
        try {
            // 模拟插件执行逻辑（不实际调用API）
            const params = testCase.input;
            
            switch (params.command) {
                case 'geocoding':
                    if (!params.query) {
                        throw new Error('Query parameter is required for geocoding');
                    }
                    console.log('     ✅ 参数验证通过');
                    console.log(`     🔍 查询: "${params.query}"`);
                    console.log(`     📊 限制结果数: ${params.limit || 5}`);
                    break;
                    
                case 'reverse_geocoding':
                    if (params.longitude === undefined || params.latitude === undefined) {
                        throw new Error('Longitude and latitude parameters are required for reverse geocoding');
                    }
                    console.log('     ✅ 参数验证通过');
                    console.log(`     📐 坐标: [${params.longitude}, ${params.latitude}]`);
                    break;
                    
                case 'directions':
                    if (!params.coordinates || !Array.isArray(params.coordinates)) {
                        throw new Error('Coordinates array is required for directions');
                    }
                    if (params.coordinates.length < 2) {
                        throw new Error('At least 2 coordinates are required for directions');
                    }
                    console.log('     ✅ 参数验证通过');
                    console.log(`     📍 坐标点数: ${params.coordinates.length}`);
                    console.log(`     🚗 出行方式: ${params.profile || 'driving'}`);
                    break;
                    
                case 'matrix':
                    if (!params.coordinates || !Array.isArray(params.coordinates)) {
                        throw new Error('Coordinates array is required for matrix');
                    }
                    if (params.coordinates.length < 2) {
                        throw new Error('At least 2 coordinates are required for matrix');
                    }
                    console.log('     ✅ 参数验证通过');
                    console.log(`     📍 坐标点数: ${params.coordinates.length}`);
                    console.log(`     🚗 出行方式: ${params.profile || 'driving'}`);
                    break;
                    
                case 'static_map':
                    console.log('     ✅ 参数验证通过');
                    console.log(`     📐 中心点: [${params.longitude || 116.3974}, ${params.latitude || 39.9093}]`);
                    console.log(`     🔍 缩放级别: ${params.zoom || 12}`);
                    console.log(`     📏 尺寸: ${params.width || 600}x${params.height || 400}`);
                    break;
                    
                default:
                    throw new Error(`Unknown command: ${params.command}`);
            }
            
        } catch (error) {
            console.log(`     ❌ 错误: ${error.message}`);
        }
    }
    
    // 测试3: JSON格式验证
    console.log('\n📋 测试3: JSON输出格式验证');
    
    const mockSuccessResult = {
        status: 'success',
        result: {
            query: '北京天安门',
            results: [
                {
                    place_name: '天安门广场, 北京市, 中国',
                    place_name_zh: '天安门广场',
                    center: [116.3974, 39.9093],
                    bbox: [116.3874, 39.8993, 116.4074, 39.9193],
                    properties: {
                        address: '天安门广场',
                        category: 'landmark'
                    }
                }
            ],
            result_count: 1
        },
        messageForAI: 'Mapbox geocoding completed successfully.'
    };
    
    const mockErrorResult = {
        status: 'error',
        error: 'Access Token not configured',
        messageForAI: 'Mapbox API error: Access Token not configured'
    };
    
    console.log('   ✅ 成功响应格式:');
    console.log('     ', JSON.stringify(mockSuccessResult, null, 6).substring(0, 200) + '...');
    
    console.log('   ✅ 错误响应格式:');
    console.log('     ', JSON.stringify(mockErrorResult, null, 6));
    
    // 测试4: VCP工具调用格式
    console.log('\n📋 测试4: VCP工具调用格式示例');
    
    const vcpExamples = [
        {
            name: '地理编码 - 搜索北京天安门',
            format: `<<<[TOOL_REQUEST]>>>
tool_name:「始」MapboxService「末」,
command:「始」geocoding「末」,
query:「始」北京天安门广场「末」,
limit:「始」5「末」
<<<[END_TOOL_REQUEST]>>>`
        },
        {
            name: '路线规划 - 天安门到故宫',
            format: `<<<[TOOL_REQUEST]>>>
tool_name:「始」MapboxService「末」,
command:「始」directions「末」,
coordinates:「始」[[116.3974,39.9093],[116.3972,39.9163]]「末」,
profile:「始」walking「末」
<<<[END_TOOL_REQUEST]>>>`
        },
        {
            name: '静态地图 - 生成北京地图',
            format: `<<<[TOOL_REQUEST]>>>
tool_name:「始」MapboxService「末」,
command:「始」static_map「末」,
longitude:「始」116.3974「末」,
latitude:「始」39.9093「末」,
zoom:「始」15「末」,
markers:「始」[{"longitude":116.3974,"latitude":39.9093,"color":"red"}]「末」
<<<[END_TOOL_REQUEST]>>>`
        }
    ];
    
    for (const example of vcpExamples) {
        console.log(`\n   📝 ${example.name}:`);
        console.log(example.format);
    }
    
    console.log('\n🎉 模拟测试完成！');
    console.log('\n📋 测试总结:');
    console.log('✅ 配置加载功能正常');
    console.log('✅ 参数验证逻辑正确');
    console.log('✅ JSON输出格式符合VCP标准');
    console.log('✅ VCP工具调用格式正确');
    
    console.log('\n💡 功能特性:');
    console.log('🗺️ 地理编码: 地址转坐标');
    console.log('🔄 反向地理编码: 坐标转地址');
    console.log('🛣️ 路线规划: 多点路径计算');
    console.log('📊 距离矩阵: 批量距离计算');
    console.log('🖼️ 静态地图: 地图图片生成');
    
    if (mapbox.accessToken && !mapbox.accessToken.includes('test_token_here')) {
        console.log('\n⚠️ API测试说明:');
        console.log('配置了有效的Access Token，可以进行实际API测试');
        console.log('运行 npm test 进行完整功能测试');
    } else {
        console.log('\n⚠️ 配置提醒:');
        console.log('请在config.env中设置有效的MAPBOX_ACCESS_TOKEN');
        console.log('获取地址: https://account.mapbox.com/');
    }
}

mockTest().catch(console.error);
